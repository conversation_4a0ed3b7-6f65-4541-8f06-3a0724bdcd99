#!/usr/bin/env node

const axios = require('axios');
const fs = require('fs');
const path = require('path');
const readline = require('readline');

const API_BASE = 'http://localhost:3001';

/**
 * 密码登录助手
 */

function askQuestion(question) {
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
  });
  
  return new Promise((resolve) => {
    rl.question(question, (answer) => {
      rl.close();
      resolve(answer);
    });
  });
}

async function passwordLogin() {
  try {
    console.log('🔐 酷狗音乐密码登录');
    console.log('================================');
    
    const username = await askQuestion('请输入用户名（手机号/邮箱）: ');
    const password = await askQuestion('请输入密码: ');
    
    if (!username || !password) {
      console.log('❌ 用户名和密码不能为空');
      return;
    }
    
    console.log('🔄 正在登录...');
    
    const response = await axios.post(`${API_BASE}/login`, {
      username: username,
      password: password
    });
    
    console.log('📊 登录响应状态:', response.status);
    console.log('📋 登录结果:');
    console.log(JSON.stringify(response.data, null, 2));
    
    if (response.data.status === 1) {
      console.log('✅ 登录成功！');
      
      // 保存登录信息
      await saveLoginInfo(response.data, response.headers['set-cookie'] || []);
      
    } else {
      console.log('❌ 登录失败:', response.data.msg || '未知错误');
    }
    
  } catch (error) {
    console.error('❌ 登录过程出错:', error.message);
    if (error.response) {
      console.log('📋 错误详情:', JSON.stringify(error.response.data, null, 2));
    }
  }
}

async function saveLoginInfo(loginData, cookies) {
  try {
    console.log('💾 正在保存登录信息...');
    
    const data = loginData.data || {};
    
    console.log('👤 用户ID:', data.userid);
    console.log('💎 VIP类型:', data.vip_type);
    console.log('🎫 Token:', data.token ? '已获取' : '未获取');
    
    // 构建完整的cookie对象
    const cookieObj = {};
    
    // 从响应的cookie数组中解析
    cookies.forEach(cookieStr => {
      const [key, value] = cookieStr.split('=');
      if (key && value) {
        cookieObj[key] = value.split(';')[0]; // 移除cookie属性
      }
    });
    
    // 添加登录返回的重要信息
    if (data.token) cookieObj.token = data.token;
    if (data.userid) cookieObj.userid = data.userid;
    if (data.vip_type !== undefined) cookieObj.vip_type = data.vip_type;
    if (data.vip_token) cookieObj.vip_token = data.vip_token;
    
    // 读取现有的cookie文件（如果存在）
    const cookieFilePath = path.join(__dirname, 'kugou.txt');
    let existingCookie = {};
    
    if (fs.existsSync(cookieFilePath)) {
      try {
        const existingContent = fs.readFileSync(cookieFilePath, 'utf8').trim();
        existingCookie = JSON.parse(existingContent);
        console.log('📖 已读取现有Cookie文件');
      } catch (e) {
        console.log('⚠️  现有Cookie文件格式不正确，将覆盖');
      }
    }
    
    // 合并cookie（新的覆盖旧的）
    const finalCookie = { ...existingCookie, ...cookieObj };
    
    // 保存到文件
    fs.writeFileSync(cookieFilePath, JSON.stringify(finalCookie, null, 2));
    
    console.log('✅ 登录信息已保存到 kugou.txt');
    console.log('🔑 保存的关键信息:');
    console.log('   - userid:', finalCookie.userid);
    console.log('   - vip_type:', finalCookie.vip_type);
    console.log('   - token:', finalCookie.token ? '已保存' : '未获取');
    console.log('   - vip_token:', finalCookie.vip_token ? '已保存' : '未获取');
    
    return finalCookie;
    
  } catch (error) {
    console.error('❌ 保存登录信息失败:', error.message);
    throw error;
  }
}

async function main() {
  try {
    // 检查服务器是否运行
    try {
      await axios.get(`${API_BASE}/`);
    } catch (e) {
      console.error('❌ 请先启动酷狗音乐API服务器 (PORT=3001 node app.js)');
      process.exit(1);
    }
    
    await passwordLogin();
    
    console.log('🎉 登录完成！现在可以重启API服务器使用VIP功能了');
    
  } catch (error) {
    console.error('❌ 登录过程出错:', error.message);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = { passwordLogin, saveLoginInfo };
