#!/usr/bin/env node

const axios = require('axios');

const API_BASE = 'http://localhost:3001';

/**
 * 测试VIP功能是否正常工作
 */

async function testVipSongUrl() {
  try {
    console.log('🎵 测试VIP歌曲下载链接获取...');
    
    // 使用一个已知的VIP歌曲hash进行测试
    // 这里使用一个示例hash，你可以替换为实际的VIP歌曲hash
    const testHash = 'ABCDEF1234567890'; // 替换为实际的歌曲hash
    
    const response = await axios.get(`${API_BASE}/song/url?hash=${testHash}`);
    
    console.log('📊 API响应状态:', response.status);
    console.log('📋 响应数据:');
    console.log(JSON.stringify(response.data, null, 2));
    
    if (response.data.data && response.data.data.backupUrl) {
      console.log('✅ VIP功能正常！获取到了backupUrl');
      console.log('🔗 下载链接:', response.data.data.backupUrl);
    } else if (response.data.data && response.data.data.url) {
      console.log('⚠️  获取到普通下载链接，但没有backupUrl');
      console.log('🔗 下载链接:', response.data.data.url);
    } else {
      console.log('❌ 没有获取到下载链接，可能需要VIP权限');
    }
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    if (error.response) {
      console.log('📋 错误响应:', JSON.stringify(error.response.data, null, 2));
    }
  }
}

async function testSearch() {
  try {
    console.log('🔍 测试搜索功能...');
    
    const response = await axios.get(`${API_BASE}/search?keywords=周杰伦`);
    
    console.log('📊 搜索结果状态:', response.status);
    
    if (response.data.data && response.data.data.lists && response.data.data.lists.length > 0) {
      const firstSong = response.data.data.lists[0];
      console.log('✅ 搜索成功！');
      console.log('🎵 第一首歌:', firstSong.SongName, '-', firstSong.SingerName);
      console.log('🔑 歌曲Hash:', firstSong.FileHash);
      
      // 使用搜索到的歌曲hash测试下载
      console.log('\n🔄 使用搜索到的歌曲测试下载...');
      await testSpecificSong(firstSong.FileHash);
      
    } else {
      console.log('❌ 搜索失败或没有结果');
    }
    
  } catch (error) {
    console.error('❌ 搜索测试失败:', error.message);
  }
}

async function testSpecificSong(hash) {
  try {
    const response = await axios.get(`${API_BASE}/song/url?hash=${hash}`);
    
    console.log('📊 歌曲URL获取状态:', response.status);
    
    if (response.data.data) {
      const data = response.data.data;
      console.log('📋 歌曲信息:');
      console.log('   - 文件名:', data.fileName || '未知');
      console.log('   - 时长:', data.timeLength || '未知');
      console.log('   - 比特率:', data.bitRate || '未知');
      
      if (data.url) {
        console.log('✅ 获取到下载链接!');
        console.log('🔗 主链接:', data.url);
      }
      
      if (data.backupUrl && data.backupUrl.length > 0) {
        console.log('🎉 获取到VIP备用链接!');
        console.log('🔗 备用链接数量:', data.backupUrl.length);
        data.backupUrl.forEach((url, index) => {
          console.log(`   备用链接${index + 1}:`, url);
        });
      } else {
        console.log('⚠️  没有获取到备用链接（可能需要VIP）');
      }
      
    } else {
      console.log('❌ 没有获取到歌曲数据');
    }
    
  } catch (error) {
    console.error('❌ 歌曲URL测试失败:', error.message);
    if (error.response) {
      console.log('📋 错误详情:', JSON.stringify(error.response.data, null, 2));
    }
  }
}

async function checkCurrentCookie() {
  try {
    console.log('🍪 检查当前Cookie状态...');
    
    const fs = require('fs');
    const path = require('path');
    
    const cookieFile = path.join(__dirname, 'kugou.txt');
    
    if (fs.existsSync(cookieFile)) {
      const cookieContent = fs.readFileSync(cookieFile, 'utf8');
      const cookie = JSON.parse(cookieContent);
      
      console.log('📋 当前Cookie信息:');
      console.log('   - userid:', cookie.userid || '未设置');
      console.log('   - vip_type:', cookie.vip_type || '未设置');
      console.log('   - token:', cookie.token ? '已设置' : '未设置');
      console.log('   - vip_token:', cookie.vip_token ? '已设置' : '未设置');
      console.log('   - dfid:', cookie.dfid || cookie.kg_dfid || '未设置');
      
      if (cookie.userid && cookie.token) {
        console.log('✅ Cookie看起来包含登录信息');
      } else {
        console.log('⚠️  Cookie缺少关键登录信息，建议重新登录');
      }
      
    } else {
      console.log('❌ 没有找到Cookie文件');
    }
    
  } catch (error) {
    console.error('❌ 检查Cookie失败:', error.message);
  }
}

async function main() {
  console.log('🧪 酷狗音乐VIP功能测试');
  console.log('================================');
  
  try {
    // 检查服务器是否运行
    await axios.get(`${API_BASE}/`);
    console.log('✅ API服务器运行正常\n');
  } catch (e) {
    console.error('❌ API服务器未运行，请先启动服务器');
    process.exit(1);
  }
  
  // 检查当前Cookie
  await checkCurrentCookie();
  console.log('\n' + '='.repeat(50) + '\n');
  
  // 测试搜索和下载
  await testSearch();
  
  console.log('\n🏁 测试完成！');
}

if (require.main === module) {
  main();
}
