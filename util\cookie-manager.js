const fs = require('fs');
const path = require('path');
const { cookieToJson } = require('./util');

/**
 * 全局Cookie管理器
 */
class CookieManager {
  constructor() {
    this.globalCookie = {};
    this.cookieFilePath = path.join(process.cwd(), 'kugou.txt');
  }

  /**
   * 从文件加载Cookie
   */
  loadCookieFromFile() {
    try {
      if (fs.existsSync(this.cookieFilePath)) {
        const cookieContent = fs.readFileSync(this.cookieFilePath, 'utf8').trim();
        console.log('🍪 正在加载Cookie文件:', this.cookieFilePath);
        
        // 尝试解析JSON格式的cookie
        try {
          this.globalCookie = JSON.parse(cookieContent);
          console.log('✅ Cookie加载成功 (JSON格式):', Object.keys(this.globalCookie));
        } catch (jsonError) {
          // 如果不是JSON格式，尝试解析为cookie字符串
          this.globalCookie = cookieToJson(cookieContent);
          console.log('✅ Cookie加载成功 (字符串格式):', Object.keys(this.globalCookie));
        }
      } else {
        console.log('⚠️  Cookie文件不存在:', this.cookieFilePath);
        console.log('💡 将使用空Cookie继续运行');
      }
    } catch (error) {
      console.error('❌ 加载Cookie文件失败:', error.message);
      console.log('💡 将使用空Cookie继续运行');
      this.globalCookie = {};
    }
  }

  /**
   * 获取全局Cookie
   * @returns {Object} 全局Cookie对象
   */
  getGlobalCookie() {
    return this.globalCookie;
  }

  /**
   * 合并Cookie（全局Cookie + 请求Cookie）
   * @param {Object} requestCookie 请求中的Cookie
   * @returns {Object} 合并后的Cookie
   */
  mergeCookie(requestCookie = {}) {
    return {
      ...this.globalCookie,
      ...requestCookie
    };
  }

  /**
   * 更新全局Cookie
   * @param {Object} newCookie 新的Cookie数据
   */
  updateGlobalCookie(newCookie) {
    this.globalCookie = {
      ...this.globalCookie,
      ...newCookie
    };
  }
}

// 创建单例实例
const cookieManager = new CookieManager();

module.exports = cookieManager;
