#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const axios = require('axios');

const API_BASE = 'http://localhost:3001';

/**
 * 登录助手脚本
 * 帮助获取VIP账号的完整登录信息并保存到kugou.txt
 */

async function generateQRCode() {
  try {
    console.log('🔄 正在生成二维码...');
    const response = await axios.get(`${API_BASE}/login/qr/key`);
    
    if (response.data.status === 1) {
      const qrKey = response.data.data.qrcode || response.data.data.qrcode_key || response.data.data.key;
      const qrImg = response.data.data.qrcode_img;
      
      console.log('✅ 二维码生成成功！');
      console.log('🔑 二维码Key:', qrKey);
      console.log('📱 请用酷狗音乐APP扫描以下二维码：');
      console.log('🖼️  二维码图片数据已生成（base64格式）');
      console.log('💡 你可以将二维码保存为图片文件或在浏览器中查看');
      
      // 保存二维码为HTML文件方便查看
      const htmlContent = `
<!DOCTYPE html>
<html>
<head>
    <title>酷狗音乐登录二维码</title>
    <style>
        body { text-align: center; font-family: Arial, sans-serif; padding: 20px; }
        .qr-container { margin: 20px auto; }
        .instructions { max-width: 600px; margin: 0 auto; line-height: 1.6; }
    </style>
</head>
<body>
    <h1>🎵 酷狗音乐VIP登录</h1>
    <div class="instructions">
        <p><strong>请按以下步骤操作：</strong></p>
        <ol>
            <li>打开酷狗音乐APP</li>
            <li>使用VIP账号登录</li>
            <li>扫描下方二维码</li>
            <li>确认登录授权</li>
            <li>等待脚本自动获取登录信息</li>
        </ol>
    </div>
    <div class="qr-container">
        <img src="${qrImg}" alt="登录二维码" style="border: 2px solid #ccc; padding: 10px;">
    </div>
    <p><strong>二维码Key:</strong> ${qrKey}</p>
    <p><em>请在2分钟内完成扫码登录</em></p>
</body>
</html>`;
      
      fs.writeFileSync('qr-login.html', htmlContent);
      console.log('📄 二维码已保存到 qr-login.html，可在浏览器中打开查看');
      
      return qrKey;
    } else {
      throw new Error('二维码生成失败: ' + JSON.stringify(response.data));
    }
  } catch (error) {
    console.error('❌ 生成二维码失败:', error.message);
    throw error;
  }
}

async function checkQRStatus(qrKey) {
  try {
    console.log('🔄 正在检查二维码状态...');
    const response = await axios.get(`${API_BASE}/login/qr/check?key=${qrKey}`);
    return response.data;
  } catch (error) {
    console.error('❌ 检查二维码状态失败:', error.message);
    throw error;
  }
}

async function waitForLogin(qrKey) {
  console.log('⏳ 等待扫码登录...');
  
  const maxAttempts = 110; // 最多等待2分钟
  let attempts = 0;
  
  while (attempts < maxAttempts) {
    try {
      const result = await checkQRStatus(qrKey);
      
      switch (result.data?.status) {
        case 0:
          console.log('❌ 二维码已过期，请重新生成');
          console.log('📋 重新生成二维码结果:', JSON.stringify(result, null, 2));
          return null;
          
        case 1:
          process.stdout.write('⏳ 等待扫码...\r');
          break;
          
        case 2:
          console.log('📱 检测到扫码，请在手机上确认登录...');
          break;
          
        case 4:
          console.log('✅ 登录成功！');
          console.log('📋 登录结果:', JSON.stringify(result, null, 2));
          return result;
          
        default:
          console.log('🔄 状态未知:', result.data?.status);
      }
      
      await new Promise(resolve => setTimeout(resolve, 1000)); // 等待2秒
      attempts++;

      console.log(attempts)

    } catch (error) {
      console.error('❌ 检查登录状态出错:', error.message);
      attempts++;
    }
  }
  
  console.log('⏰ 登录超时，请重试');
  return null;
}

async function saveLoginInfo(loginResult) {
  try {
    const data = loginResult.data.data;
    const cookies = loginResult.cookie || [];
    
    console.log('💾 正在保存登录信息...');
    console.log('👤 用户ID:', data.userid);
    console.log('💎 VIP类型:', data.vip_type);
    console.log('🎫 Token:', data.token ? '已获取' : '未获取');
    
    // 构建完整的cookie对象
    const cookieObj = {};
    
    // 从响应的cookie数组中解析
    cookies.forEach(cookieStr => {
      const [key, value] = cookieStr.split('=');
      if (key && value) {
        cookieObj[key] = value;
      }
    });
    
    // 添加登录返回的重要信息
    if (data.token) cookieObj.token = data.token;
    if (data.userid) cookieObj.userid = data.userid;
    if (data.vip_type !== undefined) cookieObj.vip_type = data.vip_type;
    if (data.vip_token) cookieObj.vip_token = data.vip_token;
    
    // 读取现有的cookie文件（如果存在）
    const cookieFilePath = path.join(__dirname, 'kugou.txt');
    let existingCookie = {};
    
    if (fs.existsSync(cookieFilePath)) {
      try {
        const existingContent = fs.readFileSync(cookieFilePath, 'utf8').trim();
        existingCookie = JSON.parse(existingContent);
        console.log('📖 已读取现有Cookie文件');
      } catch (e) {
        console.log('⚠️  现有Cookie文件格式不正确，将覆盖');
      }
    }
    
    // 合并cookie（新的覆盖旧的）
    const finalCookie = { ...existingCookie, ...cookieObj };
    
    // 保存到文件
    fs.writeFileSync(cookieFilePath, JSON.stringify(finalCookie, null, 2));
    
    console.log('✅ 登录信息已保存到 kugou.txt');
    console.log('🔑 保存的关键信息:');
    console.log('   - userid:', finalCookie.userid);
    console.log('   - vip_type:', finalCookie.vip_type);
    console.log('   - token:', finalCookie.token ? '已保存' : '未获取');
    console.log('   - vip_token:', finalCookie.vip_token ? '已保存' : '未获取');
    
    // 清理临时文件
    if (fs.existsSync('qr-login.html')) {
      fs.unlinkSync('qr-login.html');
      console.log('🧹 已清理临时文件');
    }
    
    return finalCookie;
    
  } catch (error) {
    console.error('❌ 保存登录信息失败:', error.message);
    throw error;
  }
}

async function main() {
  console.log('🎵 酷狗音乐VIP登录助手');
  console.log('================================');
  
  try {
    // 检查服务器是否运行
    try {
      await axios.get(`${API_BASE}/`);
    } catch (e) {
      console.error('❌ 请先启动酷狗音乐API服务器 (PORT=3001 node app.js)');
      process.exit(1);
    }
    
    // 生成二维码
    const qrKey = await generateQRCode();
    console.log(qrKey);
    // const qrKey = "6937fd249345bc8c0fb98a3e9121b1421001"
    // 等待登录
    const loginResult = await waitForLogin(qrKey);
    
    if (loginResult) {
      // 保存登录信息
      await saveLoginInfo(loginResult);
      console.log('🎉 登录完成！现在可以重启API服务器使用VIP功能了');
    } else {
      console.log('❌ 登录失败或超时');
      process.exit(1);
    }
    
  } catch (error) {
    console.error('❌ 登录过程出错:', error.message);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

module.exports = { generateQRCode, checkQRStatus, waitForLogin, saveLoginInfo };
