{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "version": "1.3.9", "description": "", "main": "main.js", "bin": "./app.js", "types": "./interface.d.ts", "pkg": {"scripts": "module/*.js", "assets": ["node_modules/axios", "node_modules/express", "node_modules/pako", "node_modules/qrcode", "node_modules/safe-decode-uri-component", "module", "public"]}, "scripts": {"dev": "nodemon --config nodemon.json index.js", "start": "node app.js", "pkgwin": "pkg . -t node14-win-x64 -C GZip -o bin/app_win --no-bytecode", "pkglinux": "pkg . -t node14-linux-x64 -C GZip -o bin/app_linux --no-bytecode", "pkgmacos": "pkg . -t node14-macos-x64 -C GZip -o bin/app_macos --no-bytecode", "pkgwin_lite": "pkg . -t node14-win-x64 --options platform=lite -C GZip -o bin/app_win --no-bytecode", "pkgjs": "esbuild index.js --bundle --minify --outfile=bin/api_js/app.js --platform=node && mkdir -p bin/api_js/util bin/api_js/module && esbuild util/*.js --bundle --minify --outdir=bin/api_js/util --platform=node && esbuild module/*.js --bundle --minify --outdir=bin/api_js/module --platform=node"}, "engines": {"node": ">=12"}, "keywords": ["酷狗音乐", "酷狗", "音乐", "酷狗音乐nodejs"], "author": "Lines", "license": "MIT", "devDependencies": {"@types/express": "^4.17.17", "@types/node": "^18.11.9", "@types/pako": "^2.0.0", "@types/qrcode": "^1.5.0", "nodemon": "^3.1.10", "pkg": "^5.8.1", "prettier": "^2.7.1", "ts-node": "^10.9.1", "typescript": "^4.9.3"}, "dependencies": {"axios": "^1.1.3", "dotenv": "^16.4.5", "esbuild": "^0.25.3", "express": "^4.18.2", "pako": "^2.1.0", "qrcode": "^1.5.3", "safe-decode-uri-component": "^1.2.1"}}