#  发行版本部署
## 多端部署
name: Build Release

## 权限配置
permissions:
  contents: write

on:
  push:
    tags:
      - v*

jobs:
  # window 端打包
  build-windows:
    name: Build for Windows
    runs-on: windows-latest
    timeout-minutes: 30
    steps:
      # 检出 Git 仓库
      - name: Check out Git repository
        uses: actions/checkout@v4
      # 安装 Node.js
      - name: Install Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18.x'
      # 安装项目依赖
      - name: Install Dependencies
        run: npm install
      # 构建
      - name: Build App for Windows
        run: npm run pkgwin
        shell: bash
      # 上传构建产物
      - name: Upload Windows artifact
        uses: actions/upload-artifact@v4
        with:
          name: app-win
          if-no-files-found: ignore
          path: |
            bin/*.exe
      #创建 GitHub Release
      - name: Create Release
        uses: softprops/action-gh-release@v2
        if: startsWith(github.ref, 'refs/tags/v')
        with:
          token: ${{ secrets.GITHUB_TOKEN}}
          draft: false
          prerelease: false
          files: bin/*.exe
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN}}

  # mac 端打包
  build-masos:
    name: Build for MacOS
    runs-on: macos-latest
    timeout-minutes: 30
    steps:
      # 检出 Git 仓库
      - name: Check out Git repository
        uses: actions/checkout@v4
      # 安装 Node.js
      - name: Install Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18.x'
      # 安装项目依赖
      - name: Install Dependencies
        run: npm install
      # 构建
      - name: Build App for Macos
        run: npm run pkgmacos
        shell: bash
      # 上传构建产物
      - name: Upload Macos artifact
        uses: actions/upload-artifact@v4
        with:
          name: app-macos
          if-no-files-found: ignore
          path: |
            bin/*
      #创建 GitHub Release
      - name: Create Release
        uses: softprops/action-gh-release@v2
        if: startsWith(github.ref, 'refs/tags/v')
        with:
          token: ${{ secrets.GITHUB_TOKEN}}
          draft: false
          prerelease: false
          files: bin/*
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN}}

  # Linux 端打包
  build-linux:
    name: Build for Linux
    runs-on: ubuntu-22.04
    timeout-minutes: 30
    steps:
      # 检出 Git 仓库
      - name: Check out Git repository
        uses: actions/checkout@v4
      # 安装 Node.js
      - name: Install Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18.x'
      # 更新 Ubuntu 软件源
      - name: Ubuntu Update with sudo
        run: sudo apt-get update
      # 安装 RPM 和 Pacman 插件
      - name: Install RPM & Pacman (on Ubuntu)
        run: |
          sudo apt-get install --no-install-recommends -y rpm &&
          sudo apt-get install --no-install-recommends -y libarchive-tools &&
          sudo apt-get install --no-install-recommends -y libopenjp2-tools
      # 安装项目依赖
      - name: Install Dependencies
        run: npm install
      # 构建
      - name: Build App for Linux
        run: npm run pkglinux
        shell: bash
      # 上传构建产物
      - name: Upload Linux artifact
        uses: actions/upload-artifact@v4
        with:
          name: app-linux
          if-no-files-found: ignore
          path: |
            bin/*
      #创建 GitHub Release
      - name: Create Release
        uses: softprops/action-gh-release@v2
        if: startsWith(github.ref, 'refs/tags/v')
        with:
          token: ${{ secrets.GITHUB_TOKEN}}
          draft: false
          prerelease: false
          files: bin/*
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN}}
